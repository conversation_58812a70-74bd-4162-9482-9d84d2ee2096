# getFace 事件调试指南

## 问题修复总结
已修复 `UdpMessage.vue` 中 `status-update` 事件监听器位置错误的问题，并添加了详细的调试日志。

## 修复内容
1. **移动事件监听器位置**：将 `status-update` 监听器从 `udp-message` 监听器内部移到外部
2. **添加调试日志**：在关键位置添加详细的日志输出，便于问题诊断

## 测试步骤

### 1. 启动应用程序
确保应用程序正在运行，HTTP 服务已启动在端口 3030。

### 2. 打开开发者控制台
在应用程序中按 F12 或右键选择"检查元素"，打开开发者控制台。

### 3. 发送测试请求
使用以下任一方法发送 getFace 状态请求：

#### 方法A：使用测试脚本
```bash
node test-getface-status.js
```

#### 方法B：使用 curl
```bash
curl -X POST http://localhost:3030/setStatus \
  -H "Content-Type: application/json" \
  -d '{"state": "getFace"}'
```

#### 方法C：在浏览器控制台执行
```javascript
fetch('http://localhost:3030/setStatus', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({state: 'getFace'})
}).then(r => r.json()).then(console.log)
```

## 预期的调试日志输出

如果修复成功，你应该在控制台看到以下日志序列：

### 1. HTTP 服务日志（主进程）
```
状态已更新为: getFace
```

### 2. 渲染进程日志（按顺序）
```
=== UdpMessage.vue 收到状态更新 ===
状态: getFace
changeStatus 函数: function
调用 changeStatus 函数...
=== cat.vue changeStatus 被调用 ===
接收到的状态: getFace
_CatStatus 实例: CatStatus { ... }
Status transition: [当前状态索引] -> 4
Queueing audio for status: getFace -> create, immediate: false
=== cat.vue changeStatus 调用完成 ===
changeStatus 调用完成
```

### 3. 音频播放日志
```
Processing audio queue: playing create (priority: 3)
Playing song type: create
Selected audio file from create: [文件名]
Playing audio: /audio/create/[文件名]
Audio playback started successfully
```

## 故障排除

### 问题1：没有收到状态更新日志
**症状**：控制台没有显示 "UdpMessage.vue 收到状态更新"
**可能原因**：
- HTTP 服务未启动或端口被占用
- 主进程到渲染进程的 IPC 通信失败
- 事件监听器未正确注册

**解决方法**：
1. 检查 HTTP 服务是否正常启动
2. 重启应用程序
3. 检查主进程控制台是否有错误信息

### 问题2：changeStatus 不是函数
**症状**：显示 "changeStatus 不是一个函数！"
**可能原因**：
- Vue 组件的 provide/inject 机制失败
- 组件挂载顺序问题

**解决方法**：
1. 检查 cat.vue 是否正确 provide 了 changeStatus
2. 检查 UdpMessage.vue 是否正确 inject 了 changeStatus
3. 确保组件层级关系正确

### 问题3：状态变化但无音频播放
**症状**：看到状态变化日志但没有音频播放
**可能原因**：
- 音频文件不存在
- 音频文件路径错误
- 浏览器音频权限问题

**解决方法**：
1. 检查 `/audio/create/` 文件夹是否存在且包含音频文件
2. 检查浏览器是否允许自动播放音频
3. 手动点击页面后再测试

### 问题4：状态优先级冲突
**症状**：getFace 状态被忽略
**可能原因**：
- 当前状态优先级高于 getFace（优先级4）
- 系统处于等待状态（isAwait = true）

**解决方法**：
1. 先发送 idle 状态重置系统
2. 等待当前音频播放完成
3. 检查当前状态和等待状态

## 手动测试按钮
应用程序界面左下角有测试按钮，可以直接测试：
- "测试CreateOver" 按钮：测试 createOver 状态
- "回到Idle" 按钮：重置到 idle 状态

## 完整的测试序列
```bash
# 1. 重置到 idle 状态
curl -X POST http://localhost:3030/setStatus -H "Content-Type: application/json" -d '{"state": "idle"}'

# 2. 等待 2 秒

# 3. 测试 getFace 状态
curl -X POST http://localhost:3030/setStatus -H "Content-Type: application/json" -d '{"state": "getFace"}'

# 4. 等待音频播放完成（约 5-10 秒）

# 5. 测试 createOver 状态
curl -X POST http://localhost:3030/setStatus -H "Content-Type: application/json" -d '{"state": "createOver"}'
```

## 移除调试日志
测试完成后，如果需要移除调试日志，可以：
1. 将 UdpMessage.vue 中的详细日志改回简单的 `console.log('收到状态更新:', status)`
2. 移除 cat.vue 中的调试日志
