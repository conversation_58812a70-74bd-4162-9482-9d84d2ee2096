# getFace 事件无法触发 changeStatus 问题修复

## 问题描述
当外部系统通过 HTTP POST 请求发送 `getFace` 状态时，无法触发 `changeStatus` 函数，导致猫咪无法播放对应的音频。

## 问题原因
在 `src/renderer/src/components/UdpMessage.vue` 文件中，`status-update` 事件监听器被错误地放置在 `udp-message` 事件监听器的内部（第 176-180 行）。这意味着只有当收到 UDP 消息时，才会注册 `status-update` 监听器，这是不正确的。

### 原始代码结构（有问题）：
```javascript
onMounted(() => {
  // 监听 UDP 消息事件
  window.electron?.ipcRenderer?.on('udp-message', async (_, data) => {
    // ... UDP 消息处理逻辑 ...
    
    // 错误：status-update 监听器在 UDP 消息监听器内部
    window.electron?.ipcRenderer?.on('status-update', (_, status) => {
      console.log('收到状态更新:', status)
      changeStatus(status)
    })
  })
})
```

## 修复方案
将 `status-update` 事件监听器移到 `udp-message` 事件监听器外部，使其在组件挂载时立即注册。

### 修复后的代码结构：
```javascript
onMounted(() => {
  // 监听 UDP 消息事件
  window.electron?.ipcRenderer?.on('udp-message', async (_, data) => {
    // ... UDP 消息处理逻辑 ...
  })

  // 修复：将 status-update 监听器移到外部
  window.electron?.ipcRenderer?.on('status-update', (_, status) => {
    console.log('收到状态更新:', status)
    changeStatus(status)
  })
})
```

## 工作流程
1. **外部系统发送请求**：通过 HTTP POST 请求到 `http://localhost:3030/setStatus`，请求体包含 `{"state": "getFace"}`
2. **HTTP 服务处理**：`src/main/services/httpService.ts` 接收请求，向渲染进程发送 `status-update` 事件
3. **渲染进程接收**：`UdpMessage.vue` 组件监听到 `status-update` 事件，调用 `changeStatus('getFace')`
4. **状态管理**：`cat.vue` 中的 `CatStatus` 类处理状态变化，将 `getFace` 映射到 `create` 音频类型
5. **音频播放**：播放 `/audio/create/` 文件夹下的随机音频文件

## 测试方法

### 方法1：使用提供的测试脚本
```bash
node test-getface-status.js
```

### 方法2：使用 curl 命令
```bash
# 测试 getFace 状态
curl -X POST http://localhost:3030/setStatus \
  -H "Content-Type: application/json" \
  -d '{"state": "getFace"}'

# 测试 createOver 状态
curl -X POST http://localhost:3030/setStatus \
  -H "Content-Type: application/json" \
  -d '{"state": "createOver"}'
```

### 方法3：使用浏览器开发者工具
```javascript
fetch('http://localhost:3030/setStatus', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({state: 'getFace'})
})
.then(response => response.json())
.then(data => console.log(data));
```

## 预期结果
修复后，当发送 `getFace` 状态时，应该能看到：

1. **控制台输出**：
   ```
   收到状态更新: getFace
   Status transition: [当前状态] -> 4
   Queueing audio for status: getFace -> create, immediate: false
   Playing song type: create
   ```

2. **音频播放**：猫咪开始播放 `/audio/create/` 文件夹下的音频文件

3. **HTTP 响应**：
   ```json
   {
     "success": true,
     "message": "状态已更新为: getFace",
     "timestamp": "2025-01-01T12:00:00.000Z"
   }
   ```

## 状态映射表
| 状态名 | 状态索引 | 音频类型 | 音频文件夹 | 优先级 |
|--------|----------|----------|------------|--------|
| idle | 0 | - | - | - |
| discover | 1 | - | - | - |
| shooting | 2 | hello | /audio/hello/ | 1 |
| catch | 3 | catch | /audio/catch/ | 2 |
| getFace | 4 | create | /audio/create/ | 3 |
| createOver | 5 | createOver | /audio/createOvers/ | 4 |
| createError | 6 | - | - | - |

## 注意事项
1. 确保应用程序正在运行且 HTTP 服务已启动（端口 3030）
2. 确保 `/audio/create/` 文件夹存在且包含音频文件
3. 状态变化有优先级机制，低优先级状态不会覆盖高优先级状态
4. 每个状态变化后会设置 20 秒超时，自动回到 idle 状态
