// 测试 getFace 状态更新的脚本
// 使用方法：node test-getface-status.js

const http = require('http');

// 测试函数：发送状态更新请求
function testStatusUpdate(status) {
  const postData = JSON.stringify({
    state: status
  });

  const options = {
    hostname: 'localhost',
    port: 3030,
    path: '/setStatus',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应头: ${JSON.stringify(res.headers)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log(`响应数据: ${data}`);
    });
  });

  req.on('error', (e) => {
    console.error(`请求遇到问题: ${e.message}`);
  });

  // 发送数据
  req.write(postData);
  req.end();
}

// 测试不同的状态
console.log('=== 开始测试状态更新 ===');

console.log('\n1. 测试 getFace 状态:');
testStatusUpdate('getFace');

setTimeout(() => {
  console.log('\n2. 测试 createOver 状态:');
  testStatusUpdate('createOver');
}, 2000);

setTimeout(() => {
  console.log('\n3. 测试 idle 状态:');
  testStatusUpdate('idle');
}, 4000);

setTimeout(() => {
  console.log('\n4. 测试无效状态:');
  testStatusUpdate('invalidStatus');
}, 6000);

console.log('\n请确保应用程序正在运行，并观察控制台输出和猫咪的反应。');
console.log('如果修复成功，你应该能看到：');
console.log('- getFace 状态会触发 create 音频播放');
console.log('- createOver 状态会触发 createOver 音频播放');
console.log('- 控制台会显示 "收到状态更新: [状态名]"');
